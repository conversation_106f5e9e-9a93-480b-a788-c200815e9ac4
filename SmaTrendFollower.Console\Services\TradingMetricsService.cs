using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Comprehensive trading metrics collection and analysis service.
/// Tracks performance, risk metrics, system health, and trading effectiveness.
/// </summary>
public sealed class TradingMetricsService : BackgroundService, ITradingMetricsService
{
    private readonly ILiveStateStore _liveStateStore;
    private readonly ILogger<TradingMetricsService> _logger;
    private readonly MetricsServiceConfig _config;

    // Metrics storage
    private readonly ConcurrentDictionary<string, PerformanceMetric> _performanceMetrics = new();
    private readonly ConcurrentQueue<TradeMetric> _tradeHistory = new();
    private readonly ConcurrentQueue<SignalMetric> _signalHistory = new();
    private readonly ConcurrentQueue<SystemMetric> _systemMetrics = new();
    private readonly ConcurrentDictionary<string, decimal> _kpiMetrics = new();

    // Aggregated statistics
    private TradingStatistics? _lastStatistics;
    private readonly SemaphoreSlim _metricsLock = new(1, 1);

    public TradingMetricsService(
        ILiveStateStore liveStateStore,
        ILogger<TradingMetricsService> logger,
        MetricsServiceConfig? config = null)
    {
        _liveStateStore = liveStateStore;
        _logger = logger;
        _config = config ?? new MetricsServiceConfig();
    }

    /// <summary>
    /// Records a performance metric
    /// </summary>
    public async Task RecordPerformanceAsync(string operation, TimeSpan duration, bool success, string? details = null)
    {
        try
        {
            var metric = new PerformanceMetric(
                operation,
                duration,
                success,
                details ?? string.Empty,
                DateTime.UtcNow
            );

            await UpdatePerformanceMetricsAsync(operation, metric);
            
            _logger.LogDebug("Recorded performance: {Operation} - {Duration}ms - {Success}",
                operation, duration.TotalMilliseconds, success ? "Success" : "Failed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record performance metric for {Operation}", operation);
        }
    }

    /// <summary>
    /// Records a trade execution
    /// </summary>
    public async Task RecordTradeAsync(string symbol, decimal entryPrice, decimal? exitPrice, 
        decimal quantity, string action, decimal pnl)
    {
        try
        {
            var metric = new TradeMetric(
                symbol,
                entryPrice,
                exitPrice,
                quantity,
                action,
                pnl,
                DateTime.UtcNow
            );

            _tradeHistory.Enqueue(metric);
            await UpdateTradingKPIsAsync(metric);

            _logger.LogInformation("Recorded trade: {Symbol} {Action} {Quantity}@{Price:F2} PnL={PnL:F2}",
                symbol, action, quantity, entryPrice, pnl);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record trade metric for {Symbol}", symbol);
        }
    }

    /// <summary>
    /// Records a signal generation event
    /// </summary>
    public async Task RecordSignalAsync(string symbol, string signalType, decimal confidence, bool executed)
    {
        try
        {
            var metric = new SignalMetric(
                symbol,
                signalType,
                confidence,
                executed,
                DateTime.UtcNow
            );

            _signalHistory.Enqueue(metric);
            await UpdateSignalKPIsAsync(metric);

            _logger.LogDebug("Recorded signal: {Symbol} {Type} Confidence={Confidence:F2} Executed={Executed}",
                symbol, signalType, confidence, executed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record signal metric for {Symbol}", symbol);
        }
    }

    /// <summary>
    /// Records a system metric
    /// </summary>
    public Task RecordSystemMetricAsync(string metricName, decimal value, string unit)
    {
        try
        {
            var metric = new SystemMetric(
                metricName,
                value,
                unit,
                DateTime.UtcNow
            );

            _systemMetrics.Enqueue(metric);
            _kpiMetrics[metricName] = value;

            _logger.LogDebug("Recorded system metric: {Metric}={Value} {Unit}",
                metricName, value, unit);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record system metric {Metric}", metricName);
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Gets current trading statistics
    /// </summary>
    public async Task<TradingStatistics> GetTradingStatisticsAsync()
    {
        await _metricsLock.WaitAsync();
        try
        {
            if (_lastStatistics != null && 
                DateTime.UtcNow - _lastStatistics.GeneratedAt < _config.StatisticsCacheExpiry)
            {
                return _lastStatistics;
            }

            var trades = _tradeHistory.ToList();
            var signals = _signalHistory.ToList();

            var totalTrades = trades.Count;
            var profitableTrades = trades.Count(t => t.PnL > 0);
            var totalPnL = trades.Sum(t => t.PnL);
            var winRate = totalTrades > 0 ? (decimal)profitableTrades / totalTrades : 0;

            var totalSignals = signals.Count;
            var executedSignals = signals.Count(s => s.Executed);
            var signalExecutionRate = totalSignals > 0 ? (decimal)executedSignals / totalSignals : 0;

            var avgConfidence = signals.Any() ? signals.Average(s => s.Confidence) : 0;

            // Calculate additional metrics
            var maxDrawdown = CalculateMaxDrawdown(trades);
            var sharpeRatio = CalculateSharpeRatio(trades);
            var avgWin = profitableTrades > 0 ? trades.Where(t => t.PnL > 0).Average(t => t.PnL) : 0;
            var avgLoss = totalTrades - profitableTrades > 0 ? trades.Where(t => t.PnL <= 0).Average(t => t.PnL) : 0;

            _lastStatistics = new TradingStatistics(
                totalTrades,
                profitableTrades,
                totalPnL,
                winRate,
                totalSignals,
                executedSignals,
                signalExecutionRate,
                avgConfidence,
                maxDrawdown,
                sharpeRatio,
                avgWin,
                avgLoss,
                DateTime.UtcNow
            );

            return _lastStatistics;
        }
        finally
        {
            _metricsLock.Release();
        }
    }

    /// <summary>
    /// Gets performance metrics summary
    /// </summary>
    public Dictionary<string, PerformanceMetric> GetPerformanceMetrics()
    {
        return new Dictionary<string, PerformanceMetric>(_performanceMetrics);
    }

    /// <summary>
    /// Gets recent system metrics
    /// </summary>
    public List<SystemMetric> GetRecentSystemMetrics(int count = 100)
    {
        return _systemMetrics.TakeLast(count).ToList();
    }

    /// <summary>
    /// Gets key performance indicators
    /// </summary>
    public Dictionary<string, decimal> GetKPIs()
    {
        var kpis = new Dictionary<string, decimal>(_kpiMetrics);
        
        // Add calculated KPIs
        if (_lastStatistics != null)
        {
            kpis["win_rate"] = _lastStatistics.WinRate;
            kpis["total_pnl"] = _lastStatistics.TotalPnL;
            kpis["sharpe_ratio"] = _lastStatistics.SharpeRatio;
            kpis["max_drawdown"] = _lastStatistics.MaxDrawdown;
        }

        return kpis;
    }

    /// <summary>
    /// Exports all metrics to JSON
    /// </summary>
    public async Task<string> ExportMetricsAsync()
    {
        await _metricsLock.WaitAsync();
        try
        {
            var export = new MetricsExport(
                await GetTradingStatisticsAsync(),
                GetPerformanceMetrics(),
                _tradeHistory.TakeLast(1000).ToList(),
                _signalHistory.TakeLast(1000).ToList(),
                GetRecentSystemMetrics(500),
                GetKPIs(),
                DateTime.UtcNow
            );

            return JsonSerializer.Serialize(export, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
        }
        finally
        {
            _metricsLock.Release();
        }
    }

    /// <summary>
    /// Background service execution
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("TradingMetricsService started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await CollectSystemMetricsAsync();
                await PersistMetricsAsync();
                await CleanupOldMetricsAsync();
                await LogPeriodicSummaryAsync();
                
                await Task.Delay(_config.CollectionInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in metrics collection");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }

        _logger.LogInformation("TradingMetricsService stopped");
    }

    /// <summary>
    /// Collects system performance metrics
    /// </summary>
    private async Task CollectSystemMetricsAsync()
    {
        try
        {
            var process = Process.GetCurrentProcess();
            
            await RecordSystemMetricAsync("memory_usage_mb", process.WorkingSet64 / 1024 / 1024, "MB");
            await RecordSystemMetricAsync("cpu_time_ms", (decimal)process.TotalProcessorTime.TotalMilliseconds, "ms");
            await RecordSystemMetricAsync("thread_count", process.Threads.Count, "count");
            await RecordSystemMetricAsync("handle_count", process.HandleCount, "count");

            // GC metrics
            await RecordSystemMetricAsync("gc_gen0_collections", GC.CollectionCount(0), "count");
            await RecordSystemMetricAsync("gc_gen1_collections", GC.CollectionCount(1), "count");
            await RecordSystemMetricAsync("gc_gen2_collections", GC.CollectionCount(2), "count");
            await RecordSystemMetricAsync("gc_total_memory", GC.GetTotalMemory(false) / 1024 / 1024, "MB");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error collecting system metrics");
        }
    }

    /// <summary>
    /// Persists metrics to storage
    /// </summary>
    private async Task PersistMetricsAsync()
    {
        try
        {
            var statistics = await GetTradingStatisticsAsync();
            await _liveStateStore.SetMarketStateAsync("trading_statistics", statistics, TimeSpan.FromHours(24));

            var kpis = GetKPIs();
            await _liveStateStore.SetMarketStateAsync("trading_kpis", kpis, TimeSpan.FromHours(24));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error persisting metrics");
        }
    }

    /// <summary>
    /// Cleans up old metrics to prevent memory leaks
    /// </summary>
    private async Task CleanupOldMetricsAsync()
    {
        await Task.Run(() =>
        {
            var cutoff = DateTime.UtcNow.Subtract(_config.MetricsRetentionPeriod);

            // Clean up old trades
            while (_tradeHistory.TryPeek(out var trade) && trade.Timestamp < cutoff)
            {
                _tradeHistory.TryDequeue(out _);
            }

            // Clean up old signals
            while (_signalHistory.TryPeek(out var signal) && signal.Timestamp < cutoff)
            {
                _signalHistory.TryDequeue(out _);
            }

            // Clean up old system metrics
            while (_systemMetrics.TryPeek(out var system) && system.Timestamp < cutoff)
            {
                _systemMetrics.TryDequeue(out _);
            }
        });
    }

    /// <summary>
    /// Logs periodic summary
    /// </summary>
    private async Task LogPeriodicSummaryAsync()
    {
        if (DateTime.UtcNow.Minute % _config.SummaryIntervalMinutes == 0)
        {
            var stats = await GetTradingStatisticsAsync();
            
            _logger.LogInformation("Trading Summary: {Trades} trades, {WinRate:P1} win rate, {PnL:F2} total PnL, " +
                                 "{Signals} signals ({ExecutionRate:P1} executed), Sharpe: {Sharpe:F2}",
                stats.TotalTrades, stats.WinRate, stats.TotalPnL, stats.TotalSignals, 
                stats.SignalExecutionRate, stats.SharpeRatio);
        }
    }

    /// <summary>
    /// Updates performance metrics
    /// </summary>
    private async Task UpdatePerformanceMetricsAsync(string operation, PerformanceMetric metric)
    {
        await _metricsLock.WaitAsync();
        try
        {
            if (_performanceMetrics.TryGetValue(operation, out var existing))
            {
                var updated = new PerformanceMetric(
                    operation,
                    existing.Duration + metric.Duration,
                    existing.Success && metric.Success,
                    $"Calls: {(existing.CallCount ?? 0) + 1}",
                    DateTime.UtcNow,
                    (existing.CallCount ?? 0) + 1
                );

                _performanceMetrics[operation] = updated;
            }
            else
            {
                _performanceMetrics[operation] = metric with { CallCount = 1 };
            }
        }
        finally
        {
            _metricsLock.Release();
        }
    }

    /// <summary>
    /// Updates trading KPIs
    /// </summary>
    private async Task UpdateTradingKPIsAsync(TradeMetric trade)
    {
        await Task.Run(() =>
        {
            _kpiMetrics.AddOrUpdate("total_pnl", trade.PnL, (key, existing) => existing + trade.PnL);
            _kpiMetrics.AddOrUpdate("total_trades", 1, (key, existing) => existing + 1);
            
            if (trade.PnL > 0)
            {
                _kpiMetrics.AddOrUpdate("profitable_trades", 1, (key, existing) => existing + 1);
            }
        });
    }

    /// <summary>
    /// Updates signal KPIs
    /// </summary>
    private async Task UpdateSignalKPIsAsync(SignalMetric signal)
    {
        await Task.Run(() =>
        {
            _kpiMetrics.AddOrUpdate("total_signals", 1, (key, existing) => existing + 1);
            
            if (signal.Executed)
            {
                _kpiMetrics.AddOrUpdate("executed_signals", 1, (key, existing) => existing + 1);
            }
        });
    }

    /// <summary>
    /// Calculates maximum drawdown
    /// </summary>
    private decimal CalculateMaxDrawdown(List<TradeMetric> trades)
    {
        if (!trades.Any()) return 0;

        var runningPnL = 0m;
        var peak = 0m;
        var maxDrawdown = 0m;

        foreach (var trade in trades.OrderBy(t => t.Timestamp))
        {
            runningPnL += trade.PnL;
            peak = Math.Max(peak, runningPnL);
            var drawdown = peak - runningPnL;
            maxDrawdown = Math.Max(maxDrawdown, drawdown);
        }

        return maxDrawdown;
    }

    /// <summary>
    /// Calculates Sharpe ratio
    /// </summary>
    private decimal CalculateSharpeRatio(List<TradeMetric> trades)
    {
        if (trades.Count < 2) return 0;

        var returns = trades.Select(t => t.PnL).ToList();
        var avgReturn = returns.Average();
        var stdDev = (decimal)Math.Sqrt(returns.Sum(r => (double)((r - avgReturn) * (r - avgReturn))) / returns.Count);

        return stdDev > 0 ? avgReturn / stdDev : 0;
    }

    public override void Dispose()
    {
        _metricsLock?.Dispose();
        base.Dispose();
    }
}

/// <summary>
/// Interface for trading metrics service
/// </summary>
public interface ITradingMetricsService
{
    Task RecordPerformanceAsync(string operation, TimeSpan duration, bool success, string? details = null);
    Task RecordTradeAsync(string symbol, decimal entryPrice, decimal? exitPrice, decimal quantity, string action, decimal pnl);
    Task RecordSignalAsync(string symbol, string signalType, decimal confidence, bool executed);
    Task RecordSystemMetricAsync(string metricName, decimal value, string unit);
    Task<TradingStatistics> GetTradingStatisticsAsync();
    Dictionary<string, PerformanceMetric> GetPerformanceMetrics();
    List<SystemMetric> GetRecentSystemMetrics(int count = 100);
    Dictionary<string, decimal> GetKPIs();
    Task<string> ExportMetricsAsync();
}

/// <summary>
/// Configuration for metrics service
/// </summary>
public record MetricsServiceConfig(
    TimeSpan CollectionInterval = default,
    TimeSpan MetricsRetentionPeriod = default,
    TimeSpan StatisticsCacheExpiry = default,
    int SummaryIntervalMinutes = 15
)
{
    public MetricsServiceConfig() : this(
        TimeSpan.FromMinutes(1), TimeSpan.FromDays(7), TimeSpan.FromMinutes(5), 15) { }
}

/// <summary>
/// Performance metric record
/// </summary>
public record PerformanceMetric(
    string Operation,
    TimeSpan Duration,
    bool Success,
    string Details,
    DateTime Timestamp,
    int? CallCount = null
);

/// <summary>
/// Trade metric record
/// </summary>
public record TradeMetric(
    string Symbol,
    decimal EntryPrice,
    decimal? ExitPrice,
    decimal Quantity,
    string Action,
    decimal PnL,
    DateTime Timestamp
);

/// <summary>
/// Signal metric record
/// </summary>
public record SignalMetric(
    string Symbol,
    string SignalType,
    decimal Confidence,
    bool Executed,
    DateTime Timestamp
);

/// <summary>
/// System metric record
/// </summary>
public record SystemMetric(
    string MetricName,
    decimal Value,
    string Unit,
    DateTime Timestamp
);

/// <summary>
/// Trading statistics summary
/// </summary>
public record TradingStatistics(
    int TotalTrades,
    int ProfitableTrades,
    decimal TotalPnL,
    decimal WinRate,
    int TotalSignals,
    int ExecutedSignals,
    decimal SignalExecutionRate,
    decimal AverageConfidence,
    decimal MaxDrawdown,
    decimal SharpeRatio,
    decimal AverageWin,
    decimal AverageLoss,
    DateTime GeneratedAt
);

/// <summary>
/// Complete metrics export
/// </summary>
public record MetricsExport(
    TradingStatistics TradingStats,
    Dictionary<string, PerformanceMetric> PerformanceMetrics,
    List<TradeMetric> RecentTrades,
    List<SignalMetric> RecentSignals,
    List<SystemMetric> RecentSystemMetrics,
    Dictionary<string, decimal> KPIs,
    DateTime ExportedAt
);
