using SmaTrendFollower.Console.Extensions;
using SmaTrendFollower.Models;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

public sealed class SignalGenerator : ISignalGenerator
{
    private readonly IMarketDataService _marketDataService;
    private readonly IUniverseProvider _universeProvider;
    private readonly ILogger<SignalGenerator> _logger;

    public SignalGenerator(
        IMarketDataService marketDataService,
        IUniverseProvider universeProvider,
        ILogger<SignalGenerator> logger)
    {
        _marketDataService = marketDataService;
        _universeProvider = universeProvider;
        _logger = logger;
    }

    public async Task<IEnumerable<TradingSignal>> RunAsync(int topN = 10)
    {
        try
        {
            // Get universe of symbols (SPY + top-500 tickers)
            var symbols = await _universeProvider.GetSymbolsAsync();
            var symbolList = symbols.ToList();

            _logger.LogInformation("Screening {Count} symbols for trading signals", symbolList.Count);

            var signals = new List<TradingSignal>();

            // Process symbols in batches to avoid API limits
            const int batchSize = 10;
            for (int i = 0; i < symbolList.Count; i += batchSize)
            {
                var batch = symbolList.Skip(i).Take(batchSize);
                var batchSignals = await ProcessBatch(batch);
                signals.AddRange(batchSignals);
            }

            // Filter and rank signals
            var filteredSignals = signals
                .Where(s => s.Price > 0 && s.Atr > 0)
                .Where(s => s.Atr / s.Price < 0.03m) // ATR/Price < 3%
                .OrderByDescending(s => s.SixMonthReturn)
                .Take(topN)
                .ToList();

            _logger.LogInformation("Generated {Count} trading signals from {Total} symbols",
                filteredSignals.Count, symbolList.Count);

            return filteredSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating trading signals");
            return Enumerable.Empty<TradingSignal>();
        }
    }

    private async Task<IEnumerable<TradingSignal>> ProcessBatch(IEnumerable<string> symbols)
    {
        var signals = new List<TradingSignal>();

        foreach (var symbol in symbols)
        {
            try
            {
                // Get 250 days of data for technical analysis
                var startDate = DateTime.UtcNow.AddDays(-300);
                var endDate = DateTime.UtcNow;

                var response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                var bars = response.Items.ToList();

                if (bars.Count < 200) // Need enough data for SMA200
                    continue;

                var currentPrice = bars.Last().Close;
                var sma50 = (decimal)bars.GetSma50();
                var sma200 = (decimal)bars.GetSma200();
                var atr14 = (decimal)bars.GetAtr14();
                var sixMonthReturn = (decimal)bars.GetTotalReturn(126); // ~6 months

                // Filter: close > sma50 && close > sma200
                if (currentPrice > sma50 && currentPrice > sma200)
                {
                    signals.Add(new TradingSignal(symbol, currentPrice, atr14, sixMonthReturn));
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing symbol {Symbol}", symbol);
            }
        }

        return signals;
    }
}
