using Xunit;
using FluentAssertions;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Tests.Models;

public class RedisModelsTests
{
    [Fact]
    public void RedisTrailingStop_GetRedisKey_ShouldReturnCorrectFormat()
    {
        // Arrange
        var symbol = "AAPL";

        // Act
        var key = RedisTrailingStop.GetRedisKey(symbol);

        // Assert
        key.Should().Be("stop:AAPL");
    }

    [Fact]
    public void RedisSignalFlag_GetRedisKey_ShouldReturnCorrectFormat()
    {
        // Arrange
        var symbol = "AAPL";
        var date = new DateTime(2024, 6, 20);

        // Act
        var key = RedisSignalFlag.GetRedisKey(symbol, date);

        // Assert
        key.Should().Be("signal:AAPL:20240620");
    }

    [Fact]
    public void RedisThrottleFlag_GetRedisKey_ShouldReturnCorrectFormat()
    {
        // Arrange
        var symbol = "AAPL";
        var date = new DateTime(2024, 6, 20);

        // Act
        var key = RedisThrottleFlag.GetRedisKey(symbol, date);

        // Assert
        key.Should().Be("block:AAPL:20240620");
    }

    [Fact]
    public void RedisTrailingStop_JsonSerialization_ShouldRoundTrip()
    {
        // Arrange
        var originalStop = new RedisTrailingStop
        {
            Symbol = "AAPL",
            StopPrice = 150.00m,
            EntryPrice = 160.00m,
            CurrentAtr = 2.50m,
            HighWaterMark = 165.00m,
            Quantity = 100m,
            LastUpdated = DateTime.UtcNow,
            EntryDate = DateTime.UtcNow.AddDays(-5),
            OrderId = "test-order-123"
        };

        // Act
        var json = originalStop.ToJson();
        var deserializedStop = RedisTrailingStop.FromJson(json);

        // Assert
        deserializedStop.Should().NotBeNull();
        deserializedStop!.Symbol.Should().Be(originalStop.Symbol);
        deserializedStop.StopPrice.Should().Be(originalStop.StopPrice);
        deserializedStop.EntryPrice.Should().Be(originalStop.EntryPrice);
        deserializedStop.CurrentAtr.Should().Be(originalStop.CurrentAtr);
        deserializedStop.HighWaterMark.Should().Be(originalStop.HighWaterMark);
        deserializedStop.Quantity.Should().Be(originalStop.Quantity);
        deserializedStop.OrderId.Should().Be(originalStop.OrderId);
    }

    [Fact]
    public void RedisSignalFlag_JsonSerialization_ShouldRoundTrip()
    {
        // Arrange
        var originalFlag = new RedisSignalFlag
        {
            Symbol = "AAPL",
            TradingDate = "2024-06-20",
            SignalTriggered = true,
            TriggeredAt = DateTime.UtcNow,
            SignalStrength = 0.85m,
            Metadata = "test metadata"
        };

        // Act
        var json = originalFlag.ToJson();
        var deserializedFlag = RedisSignalFlag.FromJson(json);

        // Assert
        deserializedFlag.Should().NotBeNull();
        deserializedFlag!.Symbol.Should().Be(originalFlag.Symbol);
        deserializedFlag.TradingDate.Should().Be(originalFlag.TradingDate);
        deserializedFlag.SignalTriggered.Should().Be(originalFlag.SignalTriggered);
        deserializedFlag.SignalStrength.Should().Be(originalFlag.SignalStrength);
        deserializedFlag.Metadata.Should().Be(originalFlag.Metadata);
    }

    [Fact]
    public void RedisThrottleFlag_JsonSerialization_ShouldRoundTrip()
    {
        // Arrange
        var originalFlag = new RedisThrottleFlag
        {
            Symbol = "AAPL",
            TradingDate = "2024-06-20",
            IsBlocked = true,
            BlockReason = "earnings",
            BlockedAt = DateTime.UtcNow,
            BlockedBy = "system"
        };

        // Act
        var json = originalFlag.ToJson();
        var deserializedFlag = RedisThrottleFlag.FromJson(json);

        // Assert
        deserializedFlag.Should().NotBeNull();
        deserializedFlag!.Symbol.Should().Be(originalFlag.Symbol);
        deserializedFlag.TradingDate.Should().Be(originalFlag.TradingDate);
        deserializedFlag.IsBlocked.Should().Be(originalFlag.IsBlocked);
        deserializedFlag.BlockReason.Should().Be(originalFlag.BlockReason);
        deserializedFlag.BlockedBy.Should().Be(originalFlag.BlockedBy);
    }

    [Fact]
    public void RedisTrailingStop_FromJson_ShouldReturnNull_WhenJsonIsEmpty()
    {
        // Act
        var result = RedisTrailingStop.FromJson("");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void RedisTrailingStop_FromJson_ShouldReturnNull_WhenJsonIsInvalid()
    {
        // Act
        var result = RedisTrailingStop.FromJson("invalid json");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void RedisSignalFlag_FromJson_ShouldReturnNull_WhenJsonIsEmpty()
    {
        // Act
        var result = RedisSignalFlag.FromJson("");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void RedisSignalFlag_FromJson_ShouldReturnNull_WhenJsonIsInvalid()
    {
        // Act
        var result = RedisSignalFlag.FromJson("invalid json");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void RedisThrottleFlag_FromJson_ShouldReturnNull_WhenJsonIsEmpty()
    {
        // Act
        var result = RedisThrottleFlag.FromJson("");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void RedisThrottleFlag_FromJson_ShouldReturnNull_WhenJsonIsInvalid()
    {
        // Act
        var result = RedisThrottleFlag.FromJson("invalid json");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void RedisWarmingConfig_Default_ShouldHaveExpectedValues()
    {
        // Act
        var config = RedisWarmingConfig.Default;

        // Assert
        config.Should().NotBeNull();
        config.EssentialSymbols.Should().Contain("SPY");
        config.EssentialSymbols.Should().Contain("AAPL");
        config.EssentialSymbols.Should().Contain("MSFT");
        config.HistoricalDays.Should().Be(7);
        config.WarmSignalFlags.Should().BeTrue();
        config.WarmThrottleFlags.Should().BeTrue();
        config.DefaultTtlHours.Should().Be(24);
        config.MaxConcurrency.Should().Be(10);
    }

    [Fact]
    public void TrailingStopRecord_ToRedisModel_ShouldMapCorrectly()
    {
        // Arrange
        var record = new TrailingStopRecord
        {
            Symbol = "AAPL",
            Date = DateTime.UtcNow.Date,
            StopPrice = 150.00m,
            EntryPrice = 160.00m,
            Atr = 2.50m,
            HighWaterMark = 165.00m,
            Quantity = 100m,
            EntryDate = DateTime.UtcNow.AddDays(-5),
            OrderId = "test-order-123",
            CreatedAt = DateTime.UtcNow,
            IsActive = true
        };

        // Act
        var redisModel = record.ToRedisModel();

        // Assert
        redisModel.Should().NotBeNull();
        redisModel.Symbol.Should().Be(record.Symbol);
        redisModel.StopPrice.Should().Be(record.StopPrice);
        redisModel.EntryPrice.Should().Be(record.EntryPrice);
        redisModel.CurrentAtr.Should().Be(record.Atr);
        redisModel.HighWaterMark.Should().Be(record.HighWaterMark);
        redisModel.Quantity.Should().Be(record.Quantity);
        redisModel.EntryDate.Should().Be(record.EntryDate);
        redisModel.OrderId.Should().Be(record.OrderId);
        redisModel.LastUpdated.Should().Be(record.CreatedAt);
    }

    [Fact]
    public void TrailingStopRecord_FromRedisModel_ShouldMapCorrectly()
    {
        // Arrange
        var redisModel = new RedisTrailingStop
        {
            Symbol = "AAPL",
            StopPrice = 150.00m,
            EntryPrice = 160.00m,
            CurrentAtr = 2.50m,
            HighWaterMark = 165.00m,
            Quantity = 100m,
            LastUpdated = DateTime.UtcNow,
            EntryDate = DateTime.UtcNow.AddDays(-5),
            OrderId = "test-order-123"
        };
        var date = DateTime.UtcNow.Date;

        // Act
        var record = TrailingStopRecord.FromRedisModel(redisModel, date);

        // Assert
        record.Should().NotBeNull();
        record.Symbol.Should().Be(redisModel.Symbol);
        record.Date.Should().Be(date);
        record.StopPrice.Should().Be(redisModel.StopPrice);
        record.EntryPrice.Should().Be(redisModel.EntryPrice);
        record.Atr.Should().Be(redisModel.CurrentAtr);
        record.HighWaterMark.Should().Be(redisModel.HighWaterMark);
        record.Quantity.Should().Be(redisModel.Quantity);
        record.EntryDate.Should().Be(redisModel.EntryDate);
        record.OrderId.Should().Be(redisModel.OrderId);
        record.CreatedAt.Should().Be(redisModel.LastUpdated);
        record.IsActive.Should().BeTrue();
    }
}
