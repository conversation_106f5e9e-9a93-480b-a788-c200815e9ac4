using SmaTrendFollower.Console.Extensions;
using SmaTrendFollower.Models;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// High-performance parallelized signal generator that maximizes CPU utilization.
/// Uses parallel processing for CPU-bound indicator calculations and async I/O for data fetching.
/// </summary>
public sealed class ParallelSignalGenerator : ISignalGenerator
{
    private readonly IMarketDataService _marketDataService;
    private readonly IUniverseProvider _universeProvider;
    private readonly ILiveStateStore _liveStateStore;
    private readonly ILogger<ParallelSignalGenerator> _logger;
    private readonly ParallelOptions _parallelOptions;

    public ParallelSignalGenerator(
        IMarketDataService marketDataService,
        IUniverseProvider universeProvider,
        ILiveStateStore liveStateStore,
        ILogger<ParallelSignalGenerator> logger)
    {
        _marketDataService = marketDataService;
        _universeProvider = universeProvider;
        _liveStateStore = liveStateStore;
        _logger = logger;

        // Configure parallel options for optimal CPU utilization
        _parallelOptions = new ParallelOptions
        {
            MaxDegreeOfParallelism = Environment.ProcessorCount, // Use all available cores
            CancellationToken = CancellationToken.None
        };
    }

    public async Task<IEnumerable<TradingSignal>> RunAsync(int topN = 10)
    {
        var totalStopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Starting parallel signal generation with {ProcessorCount} cores", Environment.ProcessorCount);

            // Step 1: Get universe of symbols
            var symbols = await _universeProvider.GetSymbolsAsync();
            var symbolList = symbols.ToList();

            _logger.LogInformation("Screening {Count} symbols for trading signals using parallel processing", symbolList.Count);

            // Step 2: Parallel data fetching with batching
            var symbolDataMap = await FetchDataInParallelAsync(symbolList);
            
            _logger.LogInformation("Fetched data for {Count}/{Total} symbols", symbolDataMap.Count, symbolList.Count);

            // Step 3: Parallel signal calculation (CPU-bound)
            var signals = await CalculateSignalsInParallelAsync(symbolDataMap);

            // Step 4: Filter and rank signals
            var filteredSignals = signals
                .Where(s => s.Price > 0 && s.Atr > 0)
                .Where(s => s.Atr / s.Price < 0.03m) // ATR/Price < 3%
                .OrderByDescending(s => s.SixMonthReturn)
                .Take(topN)
                .ToList();

            totalStopwatch.Stop();
            
            _logger.LogInformation("Generated {Count} trading signals from {Total} symbols in {ElapsedMs:F0}ms (parallel processing)", 
                filteredSignals.Count, symbolList.Count, totalStopwatch.Elapsed.TotalMilliseconds);

            return filteredSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in parallel signal generation");
            return Enumerable.Empty<TradingSignal>();
        }
    }

    /// <summary>
    /// Fetches market data for all symbols using parallel async I/O operations
    /// </summary>
    private async Task<ConcurrentDictionary<string, List<IBar>>> FetchDataInParallelAsync(List<string> symbols)
    {
        var dataStopwatch = Stopwatch.StartNew();
        var symbolDataMap = new ConcurrentDictionary<string, List<IBar>>();
        var semaphore = new SemaphoreSlim(20, 20); // Limit concurrent API calls to avoid rate limits

        // Create tasks for parallel data fetching
        var fetchTasks = symbols.Select(async symbol =>
        {
            await semaphore.WaitAsync();
            try
            {
                // Check if we already signaled this symbol today to avoid duplicate work
                var today = DateTime.UtcNow.Date;
                var alreadySignaled = await _liveStateStore.WasSignaledAsync(symbol, today);
                
                if (alreadySignaled)
                {
                    _logger.LogDebug("Skipping {Symbol} - already signaled today", symbol);
                    return;
                }

                // Get 250 days of data for technical analysis
                var startDate = DateTime.UtcNow.AddDays(-300);
                var endDate = DateTime.UtcNow;

                var response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                var bars = response.Items.ToList();

                if (bars.Count >= 200) // Need enough data for SMA200
                {
                    symbolDataMap[symbol] = bars;
                }
                else
                {
                    _logger.LogDebug("Insufficient data for {Symbol}: {Count} bars", symbol, bars.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch data for {Symbol}", symbol);
            }
            finally
            {
                semaphore.Release();
            }
        });

        await Task.WhenAll(fetchTasks);
        dataStopwatch.Stop();

        _logger.LogDebug("Data fetching completed in {ElapsedMs:F0}ms for {Count} symbols", 
            dataStopwatch.Elapsed.TotalMilliseconds, symbolDataMap.Count);

        return symbolDataMap;
    }

    /// <summary>
    /// Calculates trading signals using parallel CPU processing for indicator calculations
    /// </summary>
    private async Task<ConcurrentBag<TradingSignal>> CalculateSignalsInParallelAsync(
        ConcurrentDictionary<string, List<IBar>> symbolDataMap)
    {
        var calculationStopwatch = Stopwatch.StartNew();
        var signals = new ConcurrentBag<TradingSignal>();
        var processedCount = 0;

        // Use Parallel.ForEach for CPU-bound indicator calculations
        await Task.Run(() =>
        {
            Parallel.ForEach(symbolDataMap, _parallelOptions, kvp =>
            {
                var symbol = kvp.Key;
                var bars = kvp.Value;

                try
                {
                    // CPU-intensive indicator calculations
                    var signal = CalculateSignalForSymbol(symbol, bars);
                    if (signal.HasValue)
                    {
                        signals.Add(signal.Value);
                    }

                    var count = Interlocked.Increment(ref processedCount);
                    if (count % 50 == 0)
                    {
                        _logger.LogDebug("Processed {Count}/{Total} symbols for signal calculation", 
                            count, symbolDataMap.Count);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to calculate signal for {Symbol}", symbol);
                }
            });
        });

        calculationStopwatch.Stop();
        
        _logger.LogDebug("Signal calculation completed in {ElapsedMs:F0}ms for {Count} symbols", 
            calculationStopwatch.Elapsed.TotalMilliseconds, signals.Count);

        return signals;
    }

    /// <summary>
    /// Calculates trading signal for a single symbol (CPU-bound operations)
    /// </summary>
    private TradingSignal? CalculateSignalForSymbol(string symbol, List<IBar> bars)
    {
        try
        {
            var currentPrice = bars.Last().Close;
            
            // Parallel indicator calculations for maximum CPU utilization
            var indicatorTasks = new[]
            {
                Task.Run(() => (decimal)bars.GetSma50()),
                Task.Run(() => (decimal)bars.GetSma200()),
                Task.Run(() => (decimal)bars.GetAtr14()),
                Task.Run(() => (decimal)bars.GetTotalReturn(126)) // ~6 months
            };

            Task.WaitAll(indicatorTasks);

            var sma50 = indicatorTasks[0].Result;
            var sma200 = indicatorTasks[1].Result;
            var atr14 = indicatorTasks[2].Result;
            var sixMonthReturn = indicatorTasks[3].Result;

            // Filter: close > sma50 && close > sma200
            if (currentPrice > sma50 && currentPrice > sma200)
            {
                return new TradingSignal(symbol, currentPrice, atr14, sixMonthReturn);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error calculating indicators for {Symbol}", symbol);
            return null;
        }
    }
}

/// <summary>
/// Performance metrics for parallel signal generation
/// </summary>
public record ParallelSignalMetrics(
    int TotalSymbols,
    int ProcessedSymbols,
    int GeneratedSignals,
    double DataFetchTimeMs,
    double CalculationTimeMs,
    double TotalTimeMs,
    int CpuCores,
    double CpuUtilization
);
