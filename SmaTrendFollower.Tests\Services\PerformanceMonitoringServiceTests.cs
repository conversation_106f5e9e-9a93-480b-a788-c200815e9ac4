using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Services;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Comprehensive tests for PerformanceMonitoringService
/// Tests performance tracking, metrics collection, and monitoring functionality
/// </summary>
public class PerformanceMonitoringServiceTests : IDisposable
{
    private readonly Mock<ILogger<PerformanceMonitoringService>> _mockLogger;
    private readonly PerformanceMonitoringService _service;

    public PerformanceMonitoringServiceTests()
    {
        _mockLogger = new Mock<ILogger<PerformanceMonitoringService>>();
        _service = new PerformanceMonitoringService(_mockLogger.Object);
    }

    [Fact]
    public void RecordSuccess_ShouldTrackSuccessfulOperation()
    {
        // Arrange
        var operationName = "TestOperation";
        var elapsedMs = 100.0;

        // Act
        _service.RecordSuccess(operationName, elapsedMs);

        // Assert
        var metrics = _service.GetOperationMetrics(operationName);
        metrics.Should().NotBeNull();
        metrics!.SuccessCount.Should().Be(1);
        metrics.TotalOperations.Should().Be(1);
    }

    [Fact]
    public void RecordError_ShouldTrackFailedOperation()
    {
        // Arrange
        var operationName = "FailedOperation";
        var elapsedMs = 150.0;
        var exception = new InvalidOperationException("Test error");

        // Act
        _service.RecordError(operationName, elapsedMs, exception);

        // Assert
        var metrics = _service.GetOperationMetrics(operationName);
        metrics.Should().NotBeNull();
        metrics!.ErrorCount.Should().Be(1);
        metrics.TotalOperations.Should().Be(1);
        metrics.ErrorRate.Should().Be(1.0);
    }

    [Fact]
    public async Task TrackOperationAsync_WithSuccessfulOperation_ShouldRecordSuccess()
    {
        // Arrange
        var operationName = "AsyncOperation";
        var expectedResult = "test result";

        // Act
        var result = await _service.TrackOperationAsync(operationName, async () =>
        {
            await Task.Delay(10);
            return expectedResult;
        });

        // Assert
        result.Should().Be(expectedResult);
        var metrics = _service.GetOperationMetrics(operationName);
        metrics.Should().NotBeNull();
        metrics!.SuccessCount.Should().Be(1);
        metrics.ErrorCount.Should().Be(0);
    }

    [Fact]
    public async Task TrackOperationAsync_WithFailingOperation_ShouldRecordError()
    {
        // Arrange
        var operationName = "FailingAsyncOperation";
        var expectedException = new InvalidOperationException("Test exception");

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _service.TrackOperationAsync<string>(operationName, () => throw expectedException));

        exception.Should().Be(expectedException);
        var metrics = _service.GetOperationMetrics(operationName);
        metrics.Should().NotBeNull();
        metrics!.ErrorCount.Should().Be(1);
        metrics.SuccessCount.Should().Be(0);
    }

    [Fact]
    public void TrackOperation_WithSuccessfulOperation_ShouldRecordSuccess()
    {
        // Arrange
        var operationName = "SyncOperation";
        var expectedResult = 42;

        // Act
        var result = _service.TrackOperation(operationName, () =>
        {
            Thread.Sleep(10);
            return expectedResult;
        });

        // Assert
        result.Should().Be(expectedResult);
        var metrics = _service.GetOperationMetrics(operationName);
        metrics.Should().NotBeNull();
        metrics!.SuccessCount.Should().Be(1);
        metrics.ErrorCount.Should().Be(0);
    }

    [Fact]
    public void GetSystemMetrics_ShouldReturnValidMetrics()
    {
        // Act
        var systemMetrics = _service.GetSystemMetrics();

        // Assert
        systemMetrics.Should().NotBeNull();
        systemMetrics.ProcessorCount.Should().BeGreaterThan(0);
        systemMetrics.ThreadCount.Should().BeGreaterThan(0);
        systemMetrics.WorkingSetMB.Should().BeGreaterThan(0);
    }

    [Fact]
    public void GetAllOperationMetrics_ShouldReturnAllOperations()
    {
        // Arrange
        _service.RecordSuccess("Operation1", 100.0);
        _service.RecordSuccess("Operation2", 200.0);
        _service.RecordError("Operation3", 150.0, new Exception("Test"));

        // Act
        var allMetrics = _service.GetAllOperationMetrics();

        // Assert
        allMetrics.Should().NotBeNull();
        allMetrics.Should().HaveCount(3);
        allMetrics.Should().Contain(m => m.OperationName == "Operation1");
        allMetrics.Should().Contain(m => m.OperationName == "Operation2");
        allMetrics.Should().Contain(m => m.OperationName == "Operation3");
    }

    [Fact]
    public void GetOperationMetrics_WithNonExistentOperation_ShouldReturnNull()
    {
        // Act
        var metrics = _service.GetOperationMetrics("NonExistentOperation");

        // Assert
        metrics.Should().BeNull();
    }

    [Fact]
    public void MultipleSuccessRecords_ShouldAggregateCorrectly()
    {
        // Arrange
        var operationName = "MultipleSuccessOperation";

        // Act
        _service.RecordSuccess(operationName, 100.0);
        _service.RecordSuccess(operationName, 200.0);
        _service.RecordSuccess(operationName, 300.0);

        // Assert
        var metrics = _service.GetOperationMetrics(operationName);
        metrics.Should().NotBeNull();
        metrics!.TotalOperations.Should().Be(3);
        metrics.SuccessCount.Should().Be(3);
        metrics.ErrorCount.Should().Be(0);
        metrics.AverageLatencyMs.Should().Be(200.0); // (100 + 200 + 300) / 3
    }

    [Fact]
    public void MixedSuccessAndError_ShouldCalculateCorrectRates()
    {
        // Arrange
        var operationName = "MixedOperation";

        // Act
        _service.RecordSuccess(operationName, 100.0);
        _service.RecordSuccess(operationName, 150.0);
        _service.RecordError(operationName, 200.0, new Exception("Test"));

        // Assert
        var metrics = _service.GetOperationMetrics(operationName);
        metrics.Should().NotBeNull();
        metrics!.TotalOperations.Should().Be(3);
        metrics.SuccessCount.Should().Be(2);
        metrics.ErrorCount.Should().Be(1);
        metrics.ErrorRate.Should().BeApproximately(1.0 / 3.0, 0.001);
    }

    [Fact]
    public async Task ConcurrentOperations_ShouldHandleSafely()
    {
        // Arrange & Act
        var tasks = new List<Task>();
        for (int i = 0; i < 10; i++)
        {
            var index = i;
            tasks.Add(Task.Run(() => _service.RecordSuccess($"ConcurrentOp{index}", index * 10.0)));
        }

        await Task.WhenAll(tasks);

        // Assert
        var allMetrics = _service.GetAllOperationMetrics();
        allMetrics.Should().HaveCount(10);
    }

    [Fact]
    public void TrackOperation_WithException_ShouldPropagateException()
    {
        // Arrange
        var operationName = "ExceptionOperation";
        var expectedException = new InvalidOperationException("Test exception");

        // Act & Assert
        var exception = Assert.Throws<InvalidOperationException>(() =>
            _service.TrackOperation<string>(operationName, () => throw expectedException));

        exception.Should().Be(expectedException);
        var metrics = _service.GetOperationMetrics(operationName);
        metrics.Should().NotBeNull();
        metrics!.ErrorCount.Should().Be(1);
    }

    [Fact]
    public void PerformanceMetrics_ShouldCalculatePercentiles()
    {
        // Arrange
        var operationName = "PercentileOperation";

        // Act - Record various latencies
        _service.RecordSuccess(operationName, 100.0);
        _service.RecordSuccess(operationName, 200.0);
        _service.RecordSuccess(operationName, 300.0);
        _service.RecordSuccess(operationName, 400.0);
        _service.RecordSuccess(operationName, 500.0);

        // Assert
        var metrics = _service.GetOperationMetrics(operationName);
        metrics.Should().NotBeNull();
        metrics!.MinLatencyMs.Should().Be(100.0);
        metrics.MaxLatencyMs.Should().Be(500.0);
        metrics.AverageLatencyMs.Should().Be(300.0);
        metrics.P50LatencyMs.Should().BeGreaterThan(0);
        metrics.P95LatencyMs.Should().BeGreaterThan(0);
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}
