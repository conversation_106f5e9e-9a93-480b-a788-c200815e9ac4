using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using SmaTrendFollower.Models;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for dynamically building and caching tradeable symbol universes based on liquidity, volatility, and price criteria
/// </summary>
public sealed class DynamicUniverseProvider : IDynamicUniverseProvider, IDisposable
{
    private readonly IMarketDataService _marketDataService;
    private readonly IDatabase? _redis;
    private readonly ConnectionMultiplexer? _connectionMultiplexer;
    private readonly ILogger<DynamicUniverseProvider> _logger;
    private readonly UniverseFilterCriteria _defaultCriteria;
    
    // Default candidate symbols - can be expanded to include more comprehensive lists
    private static readonly string[] DefaultCandidates = new[]
    {
        // Major indices and ETFs
        "SPY", "QQQ", "IWM", "VTI", "VEA", "VWO", "AGG", "TLT", "GLD", "VIX",
        
        // Large cap tech
        "AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "TSLA", "META", "NVDA", "NFLX", "ADBE",
        "CRM", "ORCL", "INTC", "AMD", "QCOM", "AVGO", "TXN", "CSCO", "IBM", "UBER",
        
        // Large cap non-tech
        "BRK.B", "UNH", "JNJ", "XOM", "JPM", "V", "PG", "HD", "CVX", "MA",
        "ABBV", "PFE", "KO", "PEP", "TMO", "COST", "WMT", "MRK", "DIS", "ABT",
        
        // Mid cap growth
        "SHOP", "SQ", "ROKU", "ZOOM", "DOCU", "OKTA", "SNOW", "PLTR", "RBLX", "COIN",
        "HOOD", "SOFI", "AFRM", "UPST", "OPEN", "LCID", "RIVN", "F", "GM", "NIO",
        
        // Financial services
        "BAC", "WFC", "GS", "MS", "C", "USB", "PNC", "TFC", "COF", "AXP",
        
        // Healthcare & biotech
        "MRNA", "BNTX", "GILD", "AMGN", "BIIB", "REGN", "VRTX", "ILMN", "ISRG", "DHR",
        
        // Energy & commodities
        "COP", "EOG", "SLB", "HAL", "OXY", "DVN", "FANG", "MPC", "VLO", "PSX",
        
        // Consumer & retail
        "AMZN", "TSLA", "NKE", "SBUX", "MCD", "TGT", "LOW", "TJX", "BKNG", "ABNB",
        
        // Industrial & aerospace
        "BA", "CAT", "DE", "MMM", "HON", "UPS", "FDX", "LMT", "RTX", "GE"
    };

    public DynamicUniverseProvider(
        IMarketDataService marketDataService,
        IConfiguration configuration,
        ILogger<DynamicUniverseProvider> logger)
    {
        _marketDataService = marketDataService;
        _logger = logger;
        
        // Initialize Redis connection if configured (optional)
        try
        {
            var redisUrl = configuration["REDIS_URL"];
            if (!string.IsNullOrEmpty(redisUrl))
            {
                var redisDatabase = int.Parse(configuration["REDIS_DATABASE"] ?? "0");
                var redisPassword = configuration["REDIS_PASSWORD"];

                var configOptions = ConfigurationOptions.Parse(redisUrl);
                configOptions.AbortOnConnectFail = false; // Don't fail if Redis is unavailable
                if (!string.IsNullOrEmpty(redisPassword))
                {
                    configOptions.Password = redisPassword;
                }

                _connectionMultiplexer = ConnectionMultiplexer.Connect(configOptions);
                _redis = _connectionMultiplexer.GetDatabase(redisDatabase);
                _logger.LogInformation("Redis connection established for universe caching");
            }
            else
            {
                _logger.LogInformation("Redis not configured - universe caching disabled");
                _connectionMultiplexer = null;
                _redis = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to connect to Redis - universe caching disabled");
            _connectionMultiplexer = null;
            _redis = null;
        }
        
        // Initialize default filter criteria
        _defaultCriteria = new UniverseFilterCriteria
        {
            MinPrice = 10.0m,
            MinAverageVolume = 1_000_000,
            MinVolatilityPercent = 2.0m,
            AnalysisPeriodDays = 20,
            MaxSymbols = 200 // Reasonable limit for processing
        };
        
        _logger.LogInformation("DynamicUniverseProvider initialized with {CandidateCount} default candidates", 
            DefaultCandidates.Length);
    }

    public async Task<IEnumerable<string>> BuildUniverseAsync(IEnumerable<string>? candidateSymbols = null, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var candidates = (candidateSymbols ?? DefaultCandidates).ToList();
        var metrics = new UniverseGenerationMetrics();
        var filterBreakdown = new Dictionary<string, int>
        {
            ["TotalCandidates"] = candidates.Count,
            ["PassedPriceFilter"] = 0,
            ["PassedVolumeFilter"] = 0,
            ["PassedVolatilityFilter"] = 0,
            ["FinalQualified"] = 0,
            ["Errors"] = 0
        };

        _logger.LogInformation("Building universe from {CandidateCount} candidate symbols", candidates.Count);

        var qualified = new List<string>();
        var apiCallCount = 0;

        // Process symbols in batches to avoid overwhelming the API
        const int batchSize = 10;
        for (int i = 0; i < candidates.Count; i += batchSize)
        {
            var batch = candidates.Skip(i).Take(batchSize);
            var batchResults = await ProcessBatch(batch, filterBreakdown, cancellationToken);
            qualified.AddRange(batchResults.QualifiedSymbols);
            apiCallCount += batchResults.ApiCalls;
            
            // Small delay between batches to be respectful to API limits
            if (i + batchSize < candidates.Count)
            {
                await Task.Delay(100, cancellationToken);
            }
        }

        stopwatch.Stop();

        // Update metrics
        metrics.GenerationTime = stopwatch.Elapsed;
        metrics.ApiCallCount = apiCallCount;
        metrics.ErrorCount = filterBreakdown["Errors"];
        metrics.FilterBreakdown = filterBreakdown;
        metrics.AverageProcessingTime = TimeSpan.FromMilliseconds(stopwatch.Elapsed.TotalMilliseconds / candidates.Count);

        // Apply max symbols limit if specified
        if (_defaultCriteria.MaxSymbols.HasValue && qualified.Count > _defaultCriteria.MaxSymbols.Value)
        {
            qualified = qualified.Take(_defaultCriteria.MaxSymbols.Value).ToList();
            _logger.LogInformation("Limited universe to {MaxSymbols} symbols", _defaultCriteria.MaxSymbols.Value);
        }

        filterBreakdown["FinalQualified"] = qualified.Count;

        // Cache the result in Redis if available
        if (_redis != null)
        {
            try
            {
                var universeData = new RedisUniverse
                {
                    Symbols = qualified,
                    GeneratedAt = DateTime.UtcNow,
                    CandidateCount = candidates.Count,
                    QualifiedCount = qualified.Count,
                    FilterCriteria = _defaultCriteria,
                    Metrics = metrics,
                    Metadata = $"Generated from {candidates.Count} candidates in {stopwatch.Elapsed.TotalSeconds:F1}s"
                };

                await _redis.StringSetAsync(RedisUniverse.GetRedisKey(), universeData.ToJson(), TimeSpan.FromHours(24));
                _logger.LogDebug("Universe cached in Redis");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to cache universe in Redis");
            }
        }

        _logger.LogInformation("Universe built: {QualifiedCount}/{CandidateCount} symbols qualified in {ElapsedMs}ms",
            qualified.Count, candidates.Count, stopwatch.ElapsedMilliseconds);

        return qualified;
    }

    public async Task<IEnumerable<string>> GetCachedUniverseAsync(CancellationToken cancellationToken = default)
    {
        if (_redis == null)
        {
            _logger.LogDebug("Redis not available, building new universe");
            return await BuildUniverseAsync(null, cancellationToken);
        }

        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            var universeJson = await _redis.StringGetAsync(RedisUniverse.GetRedisKey());
            if (!universeJson.HasValue)
            {
                _logger.LogInformation("No cached universe found, building new universe");
                return await BuildUniverseAsync(null, cancellationToken);
            }

            var universeData = RedisUniverse.FromJson(universeJson!);
            if (universeData == null)
            {
                _logger.LogWarning("Failed to deserialize cached universe, building new universe");
                return await BuildUniverseAsync(null, cancellationToken);
            }

            _logger.LogDebug("Retrieved cached universe: {SymbolCount} symbols (generated at {GeneratedAt})",
                universeData.Symbols.Count, universeData.GeneratedAt);

            return universeData.Symbols;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cached universe, building new universe");
            return await BuildUniverseAsync(null, cancellationToken);
        }
    }

    public async Task<RedisUniverse?> GetUniverseDetailsAsync(CancellationToken cancellationToken = default)
    {
        if (_redis == null)
        {
            return null;
        }

        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            var universeJson = await _redis.StringGetAsync(RedisUniverse.GetRedisKey());
            if (!universeJson.HasValue)
            {
                return null;
            }

            return RedisUniverse.FromJson(universeJson!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving universe details");
            return null;
        }
    }

    public async Task<IEnumerable<string>> RefreshUniverseAsync(IEnumerable<string>? candidateSymbols = null, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Forcing universe refresh");
        return await BuildUniverseAsync(candidateSymbols, cancellationToken);
    }

    public async Task<bool> IsCacheValidAsync(CancellationToken cancellationToken = default)
    {
        if (_redis == null)
        {
            return false;
        }

        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            var universeJson = await _redis.StringGetAsync(RedisUniverse.GetRedisKey());
            if (!universeJson.HasValue)
            {
                return false;
            }

            var universeData = RedisUniverse.FromJson(universeJson!);
            if (universeData == null)
            {
                return false;
            }

            // Consider cache valid if generated within the last 24 hours
            var cacheAge = DateTime.UtcNow - universeData.GeneratedAt;
            return cacheAge < TimeSpan.FromHours(24);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache validity");
            return false;
        }
    }

    public IEnumerable<string> GetDefaultCandidates()
    {
        return DefaultCandidates;
    }

    private async Task<(List<string> QualifiedSymbols, int ApiCalls)> ProcessBatch(
        IEnumerable<string> symbols, 
        Dictionary<string, int> filterBreakdown, 
        CancellationToken cancellationToken)
    {
        var qualified = new List<string>();
        var apiCalls = 0;

        foreach (var symbol in symbols)
        {
            cancellationToken.ThrowIfCancellationRequested();

            try
            {
                // Get historical data for analysis
                var startDate = DateTime.UtcNow.AddDays(-_defaultCriteria.AnalysisPeriodDays);
                var endDate = DateTime.UtcNow;

                var response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                var bars = response.Items.ToList();
                apiCalls++;

                if (bars.Count < 10) // Need minimum data for analysis
                {
                    continue;
                }

                var currentPrice = bars.Last().Close;
                var volumes = bars.Select(b => (long)b.Volume).ToList();
                var closes = bars.Select(b => b.Close).ToList();

                // Apply filters
                if (!PassesPriceFilter(currentPrice))
                    continue;
                filterBreakdown["PassedPriceFilter"]++;

                if (!PassesVolumeFilter(volumes))
                    continue;
                filterBreakdown["PassedVolumeFilter"]++;

                if (!PassesVolatilityFilter(closes))
                    continue;
                filterBreakdown["PassedVolatilityFilter"]++;

                qualified.Add(symbol);
            }
            catch (Exception ex)
            {
                _logger.LogDebug("Error processing symbol {Symbol}: {Error}", symbol, ex.Message);
                filterBreakdown["Errors"]++;
            }
        }

        return (qualified, apiCalls);
    }

    private bool PassesPriceFilter(decimal price)
    {
        return price >= _defaultCriteria.MinPrice;
    }

    private bool PassesVolumeFilter(List<long> volumes)
    {
        if (volumes.Count == 0) return false;
        var averageVolume = volumes.Average();
        return averageVolume >= _defaultCriteria.MinAverageVolume;
    }

    private bool PassesVolatilityFilter(List<decimal> closes)
    {
        if (closes.Count < 2) return false;

        // Calculate daily returns
        var returns = new List<decimal>();
        for (int i = 1; i < closes.Count; i++)
        {
            var dailyReturn = (closes[i] - closes[i - 1]) / closes[i - 1];
            returns.Add(Math.Abs(dailyReturn));
        }

        if (returns.Count == 0) return false;

        // Calculate average absolute daily return as volatility proxy
        var averageVolatility = returns.Average();
        var volatilityPercent = averageVolatility * 100;

        return volatilityPercent >= _defaultCriteria.MinVolatilityPercent;
    }

    public void Dispose()
    {
        _connectionMultiplexer?.Dispose();
    }
}
