using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using SmaTrendFollower.Models;
using SmaTrendFollower.Data;
using Microsoft.EntityFrameworkCore;

namespace SmaTrendFollower.Services;

/// <summary>
/// Redis cache warming service that prepares live cache state before trading begins.
/// Loads trailing stop levels, signal flags, and daily throttle keys into Redis for fast access.
/// </summary>
public sealed class RedisWarmingService : IRedisWarmingService, IDisposable
{
    private readonly IDatabase? _redis;
    private readonly ConnectionMultiplexer? _connectionMultiplexer;
    private readonly StockBarCacheDbContext _dbContext;
    private readonly IUniverseProvider _universeProvider;
    private readonly ILogger<RedisWarmingService> _logger;
    private readonly RedisWarmingConfig _config;

    public RedisWarmingService(
        IConfiguration configuration,
        StockBarCacheDbContext dbContext,
        IUniverseProvider universeProvider,
        ILogger<RedisWarmingService> logger)
    {
        _dbContext = dbContext;
        _universeProvider = universeProvider;
        _logger = logger;
        _config = RedisWarmingConfig.Default;

        // Initialize Redis connection if configured (optional)
        try
        {
            var redisUrl = configuration["REDIS_URL"];
            if (!string.IsNullOrEmpty(redisUrl))
            {
                var redisDatabase = int.Parse(configuration["REDIS_DATABASE"] ?? "0");
                var redisPassword = configuration["REDIS_PASSWORD"];

                var configOptions = ConfigurationOptions.Parse(redisUrl);
                configOptions.AbortOnConnectFail = false; // Don't fail if Redis is unavailable
                configOptions.ConnectTimeout = 1000; // 1 second timeout for faster test execution
                configOptions.SyncTimeout = 1000; // 1 second timeout for faster test execution
                if (!string.IsNullOrEmpty(redisPassword))
                {
                    configOptions.Password = redisPassword;
                }

                _connectionMultiplexer = ConnectionMultiplexer.Connect(configOptions);
                _redis = _connectionMultiplexer.GetDatabase(redisDatabase);
                _logger.LogInformation("Redis warming service initialized with database {Database}", redisDatabase);
            }
            else
            {
                _logger.LogInformation("Redis not configured - cache warming disabled");
                _connectionMultiplexer = null;
                _redis = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to connect to Redis - cache warming disabled");
            _connectionMultiplexer = null;
            _redis = null;
        }
    }

    /// <summary>
    /// Performs complete cache warming operation
    /// </summary>
    public async Task WarmCacheAsync(CancellationToken cancellationToken = default)
    {
        if (_redis == null)
        {
            _logger.LogDebug("Redis not available - cache warming skipped");
            return;
        }

        try
        {
            _logger.LogInformation("Starting Redis cache warming...");

            var today = DateTime.UtcNow.Date;
            var yesterday = today.AddDays(-1);

            // Get symbols to warm
            var symbols = await GetSymbolsToWarmAsync();
            _logger.LogInformation("Warming cache for {Count} symbols", symbols.Count);

            // Warm in parallel with controlled concurrency
            var semaphore = new SemaphoreSlim(_config.MaxConcurrency, _config.MaxConcurrency);
            var tasks = symbols.Select(async symbol =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    await WarmSymbolCacheAsync(symbol, today, cancellationToken);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);

            _logger.LogInformation("Redis cache warming completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Redis cache warming");
            throw;
        }
    }

    /// <summary>
    /// Warms cache for a specific symbol
    /// </summary>
    private async Task WarmSymbolCacheAsync(string symbol, DateTime today, CancellationToken cancellationToken)
    {
        try
        {
            // 1. Warm trailing stop levels
            if (_config.WarmSignalFlags)
            {
                await WarmTrailingStopsAsync(symbol, cancellationToken);
            }

            // 2. Warm signal flags for today
            if (_config.WarmSignalFlags)
            {
                await WarmSignalFlagsAsync(symbol, today, cancellationToken);
            }

            // 3. Warm throttle flags
            if (_config.WarmThrottleFlags)
            {
                await WarmThrottleFlagsAsync(symbol, today, cancellationToken);
            }

            _logger.LogDebug("Cache warmed for symbol {Symbol}", symbol);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error warming cache for symbol {Symbol}", symbol);
        }
    }

    /// <summary>
    /// Loads trailing stop levels from SQLite into Redis
    /// </summary>
    private async Task WarmTrailingStopsAsync(string symbol, CancellationToken cancellationToken)
    {
        if (_redis == null) return;

        try
        {
            // Get the latest trailing stop from SQLite
            var latestStop = await _dbContext.GetLatestTrailingStopAsync(symbol);

            if (latestStop != null)
            {
                var redisStop = latestStop.ToRedisModel();
                var redisKey = RedisTrailingStop.GetRedisKey(symbol);
                var ttl = TimeSpan.FromHours(_config.DefaultTtlHours);

                await _redis.StringSetAsync(redisKey, redisStop.ToJson(), ttl);

                _logger.LogDebug("Loaded trailing stop for {Symbol}: {StopPrice:C}",
                    symbol, redisStop.StopPrice);
            }
            else
            {
                _logger.LogDebug("No trailing stop found for {Symbol}", symbol);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error warming trailing stops for {Symbol}", symbol);
        }
    }

    /// <summary>
    /// Initializes signal flags for today to prevent duplicate trades
    /// </summary>
    private async Task WarmSignalFlagsAsync(string symbol, DateTime today, CancellationToken cancellationToken)
    {
        if (_redis == null) return;

        try
        {
            var signalFlag = new RedisSignalFlag
            {
                Symbol = symbol,
                TradingDate = today.ToString("yyyy-MM-dd"),
                SignalTriggered = false,
                TriggeredAt = null,
                SignalStrength = null,
                Metadata = null
            };

            var redisKey = RedisSignalFlag.GetRedisKey(symbol, today);
            var ttl = TimeSpan.FromHours(24); // Expire at end of day

            await _redis.StringSetAsync(redisKey, signalFlag.ToJson(), ttl);

            _logger.LogDebug("Initialized signal flag for {Symbol} on {Date}", symbol, today.ToString("yyyy-MM-dd"));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error warming signal flags for {Symbol}", symbol);
        }
    }

    /// <summary>
    /// Initializes throttle flags for today
    /// </summary>
    private async Task WarmThrottleFlagsAsync(string symbol, DateTime today, CancellationToken cancellationToken)
    {
        if (_redis == null) return;

        try
        {
            var throttleFlag = new RedisThrottleFlag
            {
                Symbol = symbol,
                TradingDate = today.ToString("yyyy-MM-dd"),
                IsBlocked = false,
                BlockReason = null,
                BlockedAt = DateTime.UtcNow,
                BlockedBy = "system"
            };

            var redisKey = RedisThrottleFlag.GetRedisKey(symbol, today);
            var ttl = TimeSpan.FromHours(24); // Expire at end of day

            await _redis.StringSetAsync(redisKey, throttleFlag.ToJson(), ttl);

            _logger.LogDebug("Initialized throttle flag for {Symbol} on {Date}", symbol, today.ToString("yyyy-MM-dd"));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error warming throttle flags for {Symbol}", symbol);
        }
    }

    /// <summary>
    /// Gets the list of symbols to warm cache for
    /// </summary>
    private async Task<List<string>> GetSymbolsToWarmAsync()
    {
        var symbols = new HashSet<string>();

        // Add essential symbols
        foreach (var symbol in _config.EssentialSymbols)
        {
            symbols.Add(symbol);
        }

        // Add universe symbols
        try
        {
            var universeSymbols = await _universeProvider.GetSymbolsAsync();
            foreach (var symbol in universeSymbols)
            {
                symbols.Add(symbol);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error loading universe symbols, using essential symbols only");
        }

        return symbols.ToList();
    }

    /// <summary>
    /// Saves current Redis state to SQLite for persistence
    /// </summary>
    public async Task PersistRedisStateAsync(CancellationToken cancellationToken = default)
    {
        if (_redis == null)
        {
            _logger.LogDebug("Redis not available - state persistence skipped");
            return;
        }

        try
        {
            _logger.LogInformation("Persisting Redis state to SQLite...");

            var symbols = await GetSymbolsToWarmAsync();
            var today = DateTime.UtcNow.Date;

            foreach (var symbol in symbols)
            {
                await PersistSymbolStateAsync(symbol, today, cancellationToken);
            }

            _logger.LogInformation("Redis state persistence completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error persisting Redis state");
            throw;
        }
    }

    /// <summary>
    /// Persists Redis state for a specific symbol to SQLite
    /// </summary>
    private async Task PersistSymbolStateAsync(string symbol, DateTime date, CancellationToken cancellationToken)
    {
        if (_redis == null) return;

        try
        {
            // Persist trailing stop if it exists in Redis
            var stopKey = RedisTrailingStop.GetRedisKey(symbol);
            var stopJson = await _redis.StringGetAsync(stopKey);

            if (stopJson.HasValue)
            {
                var redisStop = RedisTrailingStop.FromJson(stopJson);
                if (redisStop != null)
                {
                    var stopRecord = TrailingStopRecord.FromRedisModel(redisStop, date);
                    await _dbContext.AddOrUpdateTrailingStopAsync(stopRecord);

                    _logger.LogDebug("Persisted trailing stop for {Symbol}", symbol);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error persisting state for {Symbol}", symbol);
        }
    }

    /// <summary>
    /// Clears all cached data from Redis
    /// </summary>
    public async Task ClearCacheAsync(CancellationToken cancellationToken = default)
    {
        if (_redis == null)
        {
            _logger.LogDebug("Redis not available - cache clearing skipped");
            return;
        }

        try
        {
            _logger.LogInformation("Clearing Redis cache...");

            var symbols = await GetSymbolsToWarmAsync();
            var today = DateTime.UtcNow.Date;

            var keys = new List<RedisKey>();

            foreach (var symbol in symbols)
            {
                keys.Add(RedisTrailingStop.GetRedisKey(symbol));
                keys.Add(RedisSignalFlag.GetRedisKey(symbol, today));
                keys.Add(RedisThrottleFlag.GetRedisKey(symbol, today));
            }

            if (keys.Any())
            {
                await _redis.KeyDeleteAsync(keys.ToArray());
                _logger.LogInformation("Cleared {Count} Redis keys", keys.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing Redis cache");
            throw;
        }
    }

    public void Dispose()
    {
        _connectionMultiplexer?.Dispose();
    }
}
