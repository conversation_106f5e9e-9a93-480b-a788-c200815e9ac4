using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Services;
using System.Text.Json;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Comprehensive tests for TradingMetricsService
/// Tests all functionality including performance tracking, trade recording, signal metrics, and statistics
/// </summary>
public class TradingMetricsServiceTests : IDisposable
{
    private readonly Mock<ILiveStateStore> _mockLiveStateStore;
    private readonly Mock<ILogger<TradingMetricsService>> _mockLogger;
    private readonly TradingMetricsService _service;
    private readonly MetricsServiceConfig _config;

    public TradingMetricsServiceTests()
    {
        _mockLiveStateStore = new Mock<ILiveStateStore>();
        _mockLogger = new Mock<ILogger<TradingMetricsService>>();
        _config = new MetricsServiceConfig(
            CollectionInterval: TimeSpan.FromSeconds(1),
            MetricsRetentionPeriod: TimeSpan.FromHours(1),
            StatisticsCacheExpiry: TimeSpan.FromMinutes(1),
            SummaryIntervalMinutes: 1
        );

        _service = new TradingMetricsService(_mockLiveStateStore.Object, _mockLogger.Object, _config);
    }

    [Fact]
    public async Task RecordPerformanceAsync_ShouldRecordMetricSuccessfully()
    {
        // Arrange
        var operation = "TestOperation";
        var duration = TimeSpan.FromMilliseconds(100);
        var success = true;
        var details = "Test details";

        // Act
        await _service.RecordPerformanceAsync(operation, duration, success, details);

        // Assert
        var metrics = _service.GetPerformanceMetrics();
        metrics.Should().ContainKey(operation);
        
        var metric = metrics[operation];
        metric.Operation.Should().Be(operation);
        metric.Duration.Should().Be(duration);
        metric.Success.Should().Be(success);
        metric.Details.Should().Be(details);
        metric.Timestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public async Task RecordPerformanceAsync_ShouldHandleExceptionGracefully()
    {
        // Arrange
        var operation = "FailingOperation";
        var duration = TimeSpan.FromMilliseconds(50);

        // Act & Assert - Should not throw
        await _service.RecordPerformanceAsync(operation, duration, false);

        // Verify logging occurred
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Debug,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Recorded performance")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task RecordTradeAsync_ShouldRecordTradeMetricSuccessfully()
    {
        // Arrange
        var symbol = "AAPL";
        var entryPrice = 150.00m;
        var exitPrice = 155.00m;
        var quantity = 100m;
        var action = "BUY";
        var pnl = 500.00m;

        // Act
        await _service.RecordTradeAsync(symbol, entryPrice, exitPrice, quantity, action, pnl);

        // Assert
        var statistics = await _service.GetTradingStatisticsAsync();
        statistics.TotalTrades.Should().Be(1);
        statistics.ProfitableTrades.Should().Be(1);
        statistics.TotalPnL.Should().Be(pnl);
        statistics.WinRate.Should().Be(1.0m);
    }

    [Fact]
    public async Task RecordTradeAsync_ShouldCalculateCorrectWinRate()
    {
        // Arrange & Act - Record profitable trade
        await _service.RecordTradeAsync("AAPL", 150m, 155m, 100m, "BUY", 500m);
        
        // Record losing trade
        await _service.RecordTradeAsync("MSFT", 200m, 195m, 50m, "BUY", -250m);

        // Assert
        var statistics = await _service.GetTradingStatisticsAsync();
        statistics.TotalTrades.Should().Be(2);
        statistics.ProfitableTrades.Should().Be(1);
        statistics.WinRate.Should().Be(0.5m);
        statistics.TotalPnL.Should().Be(250m);
    }

    [Fact]
    public async Task RecordSignalAsync_ShouldRecordSignalMetricSuccessfully()
    {
        // Arrange
        var symbol = "TSLA";
        var signalType = "BUY_SIGNAL";
        var confidence = 0.85m;
        var executed = true;

        // Act
        await _service.RecordSignalAsync(symbol, signalType, confidence, executed);

        // Assert
        var statistics = await _service.GetTradingStatisticsAsync();
        statistics.TotalSignals.Should().Be(1);
        statistics.ExecutedSignals.Should().Be(1);
        statistics.SignalExecutionRate.Should().Be(1.0m);
        statistics.AverageConfidence.Should().Be(confidence);
    }

    [Fact]
    public async Task RecordSignalAsync_ShouldCalculateCorrectExecutionRate()
    {
        // Arrange & Act - Record executed signal
        await _service.RecordSignalAsync("AAPL", "BUY", 0.9m, true);
        
        // Record non-executed signal
        await _service.RecordSignalAsync("MSFT", "SELL", 0.7m, false);

        // Assert
        var statistics = await _service.GetTradingStatisticsAsync();
        statistics.TotalSignals.Should().Be(2);
        statistics.ExecutedSignals.Should().Be(1);
        statistics.SignalExecutionRate.Should().Be(0.5m);
        statistics.AverageConfidence.Should().Be(0.8m);
    }

    [Fact]
    public async Task RecordSystemMetricAsync_ShouldRecordSystemMetricSuccessfully()
    {
        // Arrange
        var metricName = "CPU_Usage";
        var value = 75.5m;
        var unit = "Percent";

        // Act
        await _service.RecordSystemMetricAsync(metricName, value, unit);

        // Assert
        var systemMetrics = _service.GetRecentSystemMetrics(10);
        systemMetrics.Should().HaveCount(1);
        
        var metric = systemMetrics.First();
        metric.MetricName.Should().Be(metricName);
        metric.Value.Should().Be(value);
        metric.Unit.Should().Be(unit);
        metric.Timestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public async Task GetTradingStatisticsAsync_ShouldReturnEmptyStatisticsWhenNoData()
    {
        // Act
        var statistics = await _service.GetTradingStatisticsAsync();

        // Assert
        statistics.TotalTrades.Should().Be(0);
        statistics.ProfitableTrades.Should().Be(0);
        statistics.TotalPnL.Should().Be(0);
        statistics.WinRate.Should().Be(0);
        statistics.TotalSignals.Should().Be(0);
        statistics.ExecutedSignals.Should().Be(0);
        statistics.SignalExecutionRate.Should().Be(0);
        statistics.AverageConfidence.Should().Be(0);
    }

    [Fact]
    public async Task ExportMetricsAsync_ShouldReturnValidJsonWithAllMetrics()
    {
        // Arrange - Add some test data
        await _service.RecordPerformanceAsync("TestOp", TimeSpan.FromMilliseconds(100), true);
        await _service.RecordTradeAsync("AAPL", 150m, 155m, 100m, "BUY", 500m);
        await _service.RecordSignalAsync("AAPL", "BUY", 0.9m, true);
        await _service.RecordSystemMetricAsync("CPU", 50m, "%");

        // Act
        var json = await _service.ExportMetricsAsync();

        // Assert
        json.Should().NotBeNullOrEmpty();
        
        // Verify it's valid JSON
        var export = JsonSerializer.Deserialize<MetricsExport>(json, new JsonSerializerOptions 
        { 
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase 
        });
        
        export.Should().NotBeNull();
        export!.TradingStats.TotalTrades.Should().Be(1);
        export.PerformanceMetrics.Should().ContainKey("TestOp");
        export.RecentTrades.Should().HaveCount(1);
        export.RecentSignals.Should().HaveCount(1);
        export.RecentSystemMetrics.Should().HaveCount(1);
    }

    [Fact]
    public void GetKPIs_ShouldReturnEmptyDictionaryInitially()
    {
        // Act
        var kpis = _service.GetKPIs();

        // Assert
        kpis.Should().NotBeNull();
        kpis.Should().BeEmpty();
    }

    [Fact]
    public void GetRecentSystemMetrics_ShouldReturnLimitedResults()
    {
        // Arrange - Add multiple metrics
        for (int i = 0; i < 15; i++)
        {
            _service.RecordSystemMetricAsync($"Metric{i}", i * 10m, "Unit").Wait();
        }

        // Act
        var metrics = _service.GetRecentSystemMetrics(10);

        // Assert
        metrics.Should().HaveCount(10);
        metrics.Should().BeInDescendingOrder(m => m.Timestamp);
    }

    [Fact]
    public async Task GetTradingStatisticsAsync_ShouldCalculateMaxDrawdownCorrectly()
    {
        // Arrange - Create a series of trades with drawdown
        await _service.RecordTradeAsync("AAPL", 100m, 110m, 100m, "BUY", 1000m);  // +1000
        await _service.RecordTradeAsync("MSFT", 200m, 190m, 50m, "BUY", -500m);   // +500
        await _service.RecordTradeAsync("TSLA", 300m, 280m, 25m, "BUY", -500m);   // 0
        await _service.RecordTradeAsync("NVDA", 400m, 420m, 25m, "BUY", 500m);    // +500

        // Act
        var statistics = await _service.GetTradingStatisticsAsync();

        // Assert
        statistics.TotalTrades.Should().Be(4);
        statistics.TotalPnL.Should().Be(500m);
        statistics.MaxDrawdown.Should().BeLessOrEqualTo(0m); // Drawdown should be negative or zero
    }

    [Fact]
    public async Task GetTradingStatisticsAsync_ShouldCalculateAverageWinAndLoss()
    {
        // Arrange
        await _service.RecordTradeAsync("WIN1", 100m, 110m, 100m, "BUY", 1000m);
        await _service.RecordTradeAsync("WIN2", 200m, 220m, 50m, "BUY", 1000m);
        await _service.RecordTradeAsync("LOSS1", 300m, 290m, 100m, "BUY", -1000m);
        await _service.RecordTradeAsync("LOSS2", 400m, 380m, 50m, "BUY", -1000m);

        // Act
        var statistics = await _service.GetTradingStatisticsAsync();

        // Assert
        statistics.TotalTrades.Should().Be(4);
        statistics.ProfitableTrades.Should().Be(2);
        statistics.WinRate.Should().Be(0.5m);
        statistics.AverageWin.Should().Be(1000m);
        statistics.AverageLoss.Should().Be(-1000m);
    }

    [Fact]
    public async Task RecordPerformanceAsync_ShouldAggregateMultipleCallsForSameOperation()
    {
        // Arrange
        var operation = "RepeatedOperation";

        // Act - Record multiple performance metrics for same operation
        await _service.RecordPerformanceAsync(operation, TimeSpan.FromMilliseconds(100), true);
        await _service.RecordPerformanceAsync(operation, TimeSpan.FromMilliseconds(200), true);
        await _service.RecordPerformanceAsync(operation, TimeSpan.FromMilliseconds(150), false);

        // Assert
        var metrics = _service.GetPerformanceMetrics();
        metrics.Should().ContainKey(operation);

        var metric = metrics[operation];
        metric.CallCount.Should().BeGreaterThan(1);
    }

    [Fact]
    public async Task ExportMetricsAsync_ShouldLimitHistoricalDataToPreventMemoryIssues()
    {
        // Arrange - Add many trades and signals
        for (int i = 0; i < 2000; i++)
        {
            await _service.RecordTradeAsync($"SYM{i}", 100m + i, 105m + i, 10m, "BUY", 50m);
            await _service.RecordSignalAsync($"SYM{i}", "BUY", 0.8m, true);
        }

        // Act
        var json = await _service.ExportMetricsAsync();

        // Assert
        var export = JsonSerializer.Deserialize<MetricsExport>(json, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        export.Should().NotBeNull();
        export!.RecentTrades.Should().HaveCountLessOrEqualTo(1000); // Should be limited
        export.RecentSignals.Should().HaveCountLessOrEqualTo(1000); // Should be limited
    }

    [Fact]
    public async Task RecordSystemMetricAsync_ShouldMaintainChronologicalOrder()
    {
        // Arrange & Act - Add metrics with slight delays
        await _service.RecordSystemMetricAsync("Metric1", 10m, "Unit");
        await Task.Delay(10);
        await _service.RecordSystemMetricAsync("Metric2", 20m, "Unit");
        await Task.Delay(10);
        await _service.RecordSystemMetricAsync("Metric3", 30m, "Unit");

        // Assert
        var metrics = _service.GetRecentSystemMetrics(10);
        metrics.Should().HaveCount(3);
        metrics.Should().BeInDescendingOrder(m => m.Timestamp);
        metrics.First().MetricName.Should().Be("Metric3");
        metrics.Last().MetricName.Should().Be("Metric1");
    }

    [Theory]
    [InlineData(0, 0, 0)] // No trades
    [InlineData(1, 1, 1.0)] // All profitable
    [InlineData(1, 0, 0.0)] // All losing
    [InlineData(10, 7, 0.7)] // Mixed results
    public async Task GetTradingStatisticsAsync_ShouldCalculateWinRateCorrectly(
        int totalTrades, int profitableTrades, double expectedWinRate)
    {
        // Arrange
        for (int i = 0; i < profitableTrades; i++)
        {
            await _service.RecordTradeAsync($"WIN{i}", 100m, 110m, 10m, "BUY", 100m);
        }

        for (int i = 0; i < totalTrades - profitableTrades; i++)
        {
            await _service.RecordTradeAsync($"LOSS{i}", 100m, 90m, 10m, "BUY", -100m);
        }

        // Act
        var statistics = await _service.GetTradingStatisticsAsync();

        // Assert
        statistics.TotalTrades.Should().Be(totalTrades);
        statistics.ProfitableTrades.Should().Be(profitableTrades);
        statistics.WinRate.Should().Be((decimal)expectedWinRate);
    }

    [Fact]
    public async Task RecordTradeAsync_WithNullExitPrice_ShouldHandleOpenPositions()
    {
        // Arrange
        var symbol = "AAPL";
        var entryPrice = 150.00m;
        decimal? exitPrice = null; // Open position
        var quantity = 100m;
        var action = "BUY";
        var pnl = 0m; // No realized PnL yet

        // Act
        await _service.RecordTradeAsync(symbol, entryPrice, exitPrice, quantity, action, pnl);

        // Assert
        var statistics = await _service.GetTradingStatisticsAsync();
        statistics.TotalTrades.Should().Be(1);
        statistics.TotalPnL.Should().Be(0m);
    }

    [Fact]
    public async Task GetTradingStatisticsAsync_ShouldHandleConcurrentAccess()
    {
        // Arrange
        var tasks = new List<Task>();

        // Act - Simulate concurrent access
        for (int i = 0; i < 10; i++)
        {
            var index = i;
            tasks.Add(Task.Run(async () =>
            {
                await _service.RecordTradeAsync($"CONCURRENT{index}", 100m, 105m, 10m, "BUY", 50m);
                await _service.RecordSignalAsync($"CONCURRENT{index}", "BUY", 0.8m, true);
                await _service.GetTradingStatisticsAsync();
            }));
        }

        await Task.WhenAll(tasks);

        // Assert
        var statistics = await _service.GetTradingStatisticsAsync();
        statistics.TotalTrades.Should().Be(10);
        statistics.TotalSignals.Should().Be(10);
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}
