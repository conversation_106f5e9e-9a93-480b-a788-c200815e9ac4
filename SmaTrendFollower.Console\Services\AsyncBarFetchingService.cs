using Alpaca.Markets;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// High-performance asynchronous bar fetching service that maximizes bandwidth utilization.
/// Uses advanced async patterns to minimize blocking and maximize throughput.
/// </summary>
public sealed class AsyncBarFetchingService
{
    private readonly IMarketDataService _marketDataService;
    private readonly IBarStore _barStore;
    private readonly ILogger<AsyncBarFetchingService> _logger;
    private readonly SemaphoreSlim _concurrencyLimiter;
    private readonly AsyncBarFetchingConfig _config;

    public AsyncBarFetchingService(
        IMarketDataService marketDataService,
        IBarStore barStore,
        ILogger<AsyncBarFetchingService> logger,
        AsyncBarFetchingConfig? config = null)
    {
        _marketDataService = marketDataService;
        _barStore = barStore;
        _logger = logger;
        _config = config ?? new AsyncBarFetchingConfig();
        
        _concurrencyLimiter = new SemaphoreSlim(_config.MaxConcurrentRequests, _config.MaxConcurrentRequests);
    }

    /// <summary>
    /// Fetches bars for multiple symbols with maximum async efficiency
    /// </summary>
    public async Task<AsyncFetchResult> FetchBarsAsync(
        IEnumerable<string> symbols, 
        DateTime startDate, 
        DateTime endDate,
        string timeFrame = "Day",
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var symbolList = symbols.ToList();
        var results = new ConcurrentDictionary<string, FetchResult>();
        var metrics = new AsyncFetchMetrics();

        _logger.LogInformation("Starting async bar fetch for {Count} symbols ({TimeFrame})", symbolList.Count, timeFrame);

        // Create async tasks with proper cancellation support
        var fetchTasks = symbolList.Select(symbol => FetchSymbolBarsAsync(
            symbol, startDate, endDate, timeFrame, results, metrics, cancellationToken));

        try
        {
            await Task.WhenAll(fetchTasks);
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Async bar fetching was cancelled");
            throw;
        }

        stopwatch.Stop();

        var successCount = results.Values.Count(r => r.Success);
        var errorCount = results.Values.Count(r => !r.Success);
        var totalBars = results.Values.Where(r => r.Success).Sum(r => r.BarCount);
        var throughput = symbolList.Count / stopwatch.Elapsed.TotalSeconds;

        _logger.LogInformation("Async fetch completed: {Success}/{Total} symbols, {TotalBars:N0} bars in {ElapsedMs:F0}ms " +
                              "({Throughput:F1} symbols/sec)", 
            successCount, symbolList.Count, totalBars, stopwatch.Elapsed.TotalMilliseconds, throughput);

        return new AsyncFetchResult(
            symbolList.Count,
            successCount,
            errorCount,
            totalBars,
            stopwatch.Elapsed.TotalMilliseconds,
            throughput,
            results.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
        );
    }

    /// <summary>
    /// Fetches and caches bars for a single symbol with full async support
    /// </summary>
    private async Task FetchSymbolBarsAsync(
        string symbol,
        DateTime startDate,
        DateTime endDate,
        string timeFrame,
        ConcurrentDictionary<string, FetchResult> results,
        AsyncFetchMetrics metrics,
        CancellationToken cancellationToken)
    {
        await _concurrencyLimiter.WaitAsync(cancellationToken);
        var symbolStopwatch = Stopwatch.StartNew();

        try
        {
            // Check if we already have the data cached
            var hasData = await _barStore.HasBarsAsync(symbol, timeFrame, startDate, endDate, cancellationToken);
            
            if (hasData && !_config.ForceRefresh)
            {
                var cachedBars = await _barStore.LoadBarsAsync(symbol, timeFrame, startDate, endDate, cancellationToken);
                results[symbol] = new FetchResult(true, cachedBars.Count, 0, "Cache hit");
                Interlocked.Increment(ref metrics.CacheHits);
                return;
            }

            // Fetch from API
            IPage<IBar> response;
            if (timeFrame == "Day")
            {
                response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
            }
            else
            {
                response = await _marketDataService.GetStockMinuteBarsAsync(symbol, startDate, endDate);
            }

            var bars = response.Items.ToList();
            
            if (bars.Any())
            {
                // Cache the fetched data asynchronously
                await _barStore.SaveBarsAsync(symbol, timeFrame, bars, cancellationToken);
                results[symbol] = new FetchResult(true, bars.Count, symbolStopwatch.Elapsed.TotalMilliseconds, "API fetch");
                Interlocked.Increment(ref metrics.ApiFetches);
            }
            else
            {
                results[symbol] = new FetchResult(false, 0, symbolStopwatch.Elapsed.TotalMilliseconds, "No data returned");
                Interlocked.Increment(ref metrics.NoDataCount);
            }
        }
        catch (OperationCanceledException)
        {
            results[symbol] = new FetchResult(false, 0, symbolStopwatch.Elapsed.TotalMilliseconds, "Cancelled");
            throw;
        }
        catch (Exception ex)
        {
            results[symbol] = new FetchResult(false, 0, symbolStopwatch.Elapsed.TotalMilliseconds, ex.Message);
            Interlocked.Increment(ref metrics.ErrorCount);
            _logger.LogWarning(ex, "Failed to fetch bars for {Symbol}", symbol);
        }
        finally
        {
            _concurrencyLimiter.Release();
        }
    }

    /// <summary>
    /// Prefetches data for a universe of symbols to warm the cache
    /// </summary>
    public async Task PrefetchUniverseAsync(
        IEnumerable<string> symbols,
        int lookbackDays = 300,
        CancellationToken cancellationToken = default)
    {
        var endDate = DateTime.UtcNow.Date;
        var startDate = endDate.AddDays(-lookbackDays);

        _logger.LogInformation("Starting universe prefetch for {Days} days of data", lookbackDays);

        var result = await FetchBarsAsync(symbols, startDate, endDate, "Day", cancellationToken);

        _logger.LogInformation("Universe prefetch completed: {Success}/{Total} symbols cached", 
            result.SuccessCount, result.TotalSymbols);
    }

    /// <summary>
    /// Streams bars in real-time as they become available
    /// </summary>
    public async IAsyncEnumerable<SymbolBarData> StreamBarsAsync(
        IEnumerable<string> symbols,
        DateTime startDate,
        DateTime endDate,
        string timeFrame = "Day",
        [System.Runtime.CompilerServices.EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        var symbolList = symbols.ToList();
        var semaphore = new SemaphoreSlim(_config.MaxConcurrentRequests);

        var tasks = symbolList.Select(async symbol =>
        {
            await semaphore.WaitAsync(cancellationToken);
            try
            {
                var response = timeFrame == "Day" 
                    ? await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate)
                    : await _marketDataService.GetStockMinuteBarsAsync(symbol, startDate, endDate);

                return new SymbolBarData(symbol, response.Items.ToList(), true, null);
            }
            catch (Exception ex)
            {
                return new SymbolBarData(symbol, new List<IBar>(), false, ex.Message);
            }
            finally
            {
                semaphore.Release();
            }
        });

        // Yield results as they complete
        foreach (var task in tasks)
        {
            var result = await task;
            yield return result;
        }
    }
}

/// <summary>
/// Configuration for async bar fetching behavior
/// </summary>
public record AsyncBarFetchingConfig(
    int MaxConcurrentRequests = 20,
    bool ForceRefresh = false,
    TimeSpan RequestTimeout = default,
    int RetryAttempts = 3
)
{
    public AsyncBarFetchingConfig() : this(20, false, TimeSpan.FromSeconds(30), 3) { }
}

/// <summary>
/// Result of an async bar fetching operation
/// </summary>
public record AsyncFetchResult(
    int TotalSymbols,
    int SuccessCount,
    int ErrorCount,
    long TotalBars,
    double ElapsedTimeMs,
    double ThroughputSymbolsPerSecond,
    Dictionary<string, FetchResult> Results
);

/// <summary>
/// Result for a single symbol fetch
/// </summary>
public record FetchResult(
    bool Success,
    int BarCount,
    double ElapsedTimeMs,
    string Message
);

/// <summary>
/// Streaming bar data for a symbol
/// </summary>
public record SymbolBarData(
    string Symbol,
    List<IBar> Bars,
    bool Success,
    string? ErrorMessage
);

/// <summary>
/// Internal metrics tracking
/// </summary>
internal class AsyncFetchMetrics
{
    public int CacheHits;
    public int ApiFetches;
    public int ErrorCount;
    public int NoDataCount;
}
